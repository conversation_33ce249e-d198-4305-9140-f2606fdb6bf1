<script lang="ts" setup name="jlxxxb-index">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { ZjtSlxxbPageResp } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { message } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  apiIssueQx,
  apiZjtRxxxbView,
  apiZjtSlxxbCountWshLtz,
  apiZjtSlxxbPage,
  apiZjtSlxxbView,
} from '#/api';

import { QueryFormSchema, useColumns } from './data';
import _View from './view.vue';

const searchCount = ref(true);
const wshltz = ref<number>(0);

const countWshLtz = async () => {
  const res = await apiZjtSlxxbCountWshLtz({ zzlxList: '1,6', slzt: '06' });
  wshltz.value = res;
};
const onActionClick = ({
  code,
  row,
}: OnActionClickParams<ZjtSlxxbPageResp>) => {
  switch (code) {
    case 'audit': {
      handlerAudit(row);
      break;
    }
    case 'issue': {
      handlerIssue(row);
      break;
    }
    default: {
      break;
    }
  }
};

const [View, viewApi] = useVbenDrawer({
  connectedComponent: _View,
  destroyOnClose: true,
});

const handlerIssue = async (row: ZjtSlxxbPageResp) => {
  await apiIssueQx({ nbslid: row.nbslid });
  message.success('签发通过');
  gridApi?.query();
};

const handlerAudit = async (row: ZjtSlxxbPageResp) => {
  const res = await apiZjtSlxxbView({ id: row.nbslid });
  const zpRes = await apiZjtRxxxbView({ id: res.zpid });
  viewApi.setData({ ...res, base64zp: zpRes.base64zp, gridApi }).open();
};

// 根据 slfsx 字段值设置行样式
const getRowStyle = ({ row }: { row: ZjtSlxxbPageResp }) => {
  const slfsx = row.slfsx;
  if (slfsx === '80') {
    return { backgroundColor: '#7DD3FC' }; // 天蓝色 (bg-sky-300)
  } else if (slfsx >= '60' && slfsx <= '65') {
    return { backgroundColor: '#FDE047' }; // 黄色 (bg-yellow-300)
  } else if (slfsx >= '50' && slfsx <= '55') {
    return { backgroundColor: '#86EFAC' }; // 绿色 (bg-green-300)
  }
  return {};
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
  },
  gridOptions: {
    columns: useFormatterColumns(useColumns(onActionClick)),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          // formValues.slztList = ['11', '62'].join(',');
          formValues.slztDqf = true;
          formValues.hjdsjfwcx = true;
          const result = await apiZjtSlxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['pcs']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'nbslid',
      isHover: true,
    },
    rowStyle: getRowStyle,
  } as VxeTableGridOptions,
});

onMounted(async () => {
  countWshLtz();
});
</script>
<template>
  <Page>
    <Grid>
      <template #toolbar-actions>
        <!-- <a-button type="primary" size="small" @click="batchIssue()">
          批量签发
        </a-button> -->
        <span class="ml-5" :class="{ 'text-red-600': wshltz > 0 }">
          未审核绿通证: {{ wshltz }}
        </span>
      </template>
      <template #toolbar-tools>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="flex items-center space-x-1">
              <div class="h-4 w-8 border border-gray-300 bg-green-300"></div>
              <span class="text-sm">跨省办</span>
            </div>
            <div class="flex items-center space-x-1">
              <div class="h-4 w-8 border border-gray-300 bg-sky-300"></div>
              <span class="text-sm">自助办</span>
            </div>
            <div class="flex items-center space-x-1">
              <div class="h-4 w-8 border border-gray-300 bg-yellow-300"></div>
              <span class="text-sm">网办</span>
            </div>
          </div>
        </div>
      </template>
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
      <template #czsj="{ row }">
        <span class="text-red-600">{{ row.czsj }}</span>
      </template>
      <template #sldsjgsdwmc="{ row }">
        <span class="text-red-600">{{ row.sldsjgsdwmc }}</span>
      </template>
    </Grid>
    <View />
    <Form />
  </Page>
</template>

<style scoped>
/* 恢复表格行的 hover 效果 */
:deep(.vxe-table .vxe-body--row:hover) {
  background-color: var(--vxe-ui-table-row-hover-background-color) !important;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.2s ease;
  transform: scale(1.01);
}

/* 针对有自定义背景色的行，设置特殊的 hover 效果 */
:deep(
  .vxe-table .vxe-body--row[style*='background-color: rgb(135, 206, 235)']:hover
) {
  background-color: #4682b4 !important; /* 更深的钢蓝色 */
  box-shadow: 0 2px 8px rgb(70 130 180 / 30%);
  transition: all 0.2s ease;
  transform: scale(1.01);
}

:deep(
  .vxe-table .vxe-body--row[style*='background-color: rgb(255, 215, 0)']:hover
) {
  background-color: #daa520 !important; /* 更深的金黄色 */
  box-shadow: 0 2px 8px rgb(218 165 32 / 30%);
  transition: all 0.2s ease;
  transform: scale(1.01);
}

:deep(
  .vxe-table .vxe-body--row[style*='background-color: rgb(144, 238, 144)']:hover
) {
  background-color: #32cd32 !important; /* 更深的绿色 */
  box-shadow: 0 2px 8px rgb(50 205 50 / 30%);
  transition: all 0.2s ease;
  transform: scale(1.01);
}
</style>
